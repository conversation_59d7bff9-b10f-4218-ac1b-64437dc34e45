import { useState } from "react";
import { WagmiProvider } from "wagmi";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { WagmiInterface } from "./components/WagmiInterface";
import { config } from "./wagmi";

function App() {
  const [selectedLib, setSelectedLib] = useState<string | null>(null);

  const queryClient = new QueryClient();

  return (
    <div className="app-container">
      <h1>Sei ERC20 Token Interface</h1>
      <p>Choose a library implementation:</p>

      <div className="button-group">
        <button
          onClick={() => setSelectedLib("ethers")}
          className={selectedLib === "ethers" ? "active" : ""}
        >
          Ethers.js
        </button>
        <button
          onClick={() => setSelectedLib("viem")}
          className={selectedLib === "viem" ? "active" : ""}
        >
          Viem
        </button>
        <button
          onClick={() => setSelectedLib("wagmi")}
          className={selectedLib === "wagmi" ? "active" : ""}
        >
          Wagmi (React Hooks)
        </button>
      </div>

      <div className="interface-container">
        {selectedLib === "wagmi" && (
          <WagmiProvider config={config}>
            <QueryClientProvider client={queryClient}>
              <WagmiInterface />
            </QueryClientProvider>
          </WagmiProvider>
        )}
        {!selectedLib && (
          <div className="placeholder">
            <p>Select a library to see its implementation</p>
          </div>
        )}
      </div>
    </div>
  );
}

export default App;
